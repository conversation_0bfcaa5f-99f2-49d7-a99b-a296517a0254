<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.connect.ConnectFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:paddingBottom="80dp">



        <!-- Device Selection -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="USB Serial Device"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/spinner_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp" />

                <Button
                    android:id="@+id/button_refresh_devices"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Refresh Devices"
                    style="@style/CustomButton.Small" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Connection Control with Baud Rate -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp"
            android:gravity="center_vertical">

            <Spinner
                android:id="@+id/spinner_baud_rate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="12dp"
                android:background="@drawable/spinner_background" />

            <Button
                android:id="@+id/button_connection_toggle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:text="Connect"
                style="@style/CustomButton.Primary.Filled" />

        </LinearLayout>

    </LinearLayout>
</ScrollView>
