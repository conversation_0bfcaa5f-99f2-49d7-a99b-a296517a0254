package com.sartorua.thrust_tester.ui.test

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.sartorua.thrust_tester.databinding.FragmentTestBinding
import com.sartorua.thrust_tester.usb.ConnectionState
import kotlinx.coroutines.launch

class TestFragment : Fragment() {

    private var _binding: FragmentTestBinding? = null
    private lateinit var testViewModel: TestViewModel

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        testViewModel = ViewModelProvider(this, ViewModelProvider.AndroidViewModelFactory.getInstance(requireActivity().application))
            .get(TestViewModel::class.java)

        _binding = FragmentTestBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupClickListeners()
        observeViewModel()

        return root
    }

    private fun setupClickListeners() {
        binding.buttonClearData.setOnClickListener {
            testViewModel.clearReceivedData()
        }

        binding.buttonSendData.setOnClickListener {
            val data = binding.editSendData.text.toString()
            if (data.isNotEmpty()) {
                testViewModel.sendData(data + "\n") // Add newline
                binding.editSendData.text?.clear()
            }
        }
    }

    private fun observeViewModel() {
        // Observe connection state to enable/disable send button
        viewLifecycleOwner.lifecycleScope.launch {
            testViewModel.connectionState.collect { state ->
                binding.buttonSendData.isEnabled = (state == ConnectionState.CONNECTED)
            }
        }

        // Observe received data
        viewLifecycleOwner.lifecycleScope.launch {
            testViewModel.receivedData.collect { data ->
                binding.textReceivedData.text = if (data.isEmpty()) "No data received..." else data

                // Auto-scroll to bottom - improved scrolling logic
                binding.textReceivedData.post {
                    val scrollView = binding.textReceivedData.parent as? android.widget.ScrollView
                    scrollView?.fullScroll(android.widget.ScrollView.FOCUS_DOWN)
                }
            }
        }

        // Observe error messages
        viewLifecycleOwner.lifecycleScope.launch {
            testViewModel.errorMessage.collect { error ->
                error?.let {
                    Toast.makeText(requireContext(), it, Toast.LENGTH_LONG).show()
                    testViewModel.clearError()
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}